<php>if ($list) { foreach($list as $v) {
    // 改进后的年龄计算代码
    $birthday = isset($v['birthdate']) ? strtotime($v['birthdate']) : 0;
    if (!$birthday) {
        $age = '未知';
    } else {
        $currentYear = date('Y');
        $birthYear = date('Y', $birthday);
        $age = $currentYear - $birthYear;
        
        // 处理未到生日的情况
        if (date('md') < date('md', $birthday)) {
            $age--;
        }
        
        // 年龄显示逻辑
        $age = $age > 0 ? ($age < 50 ? $age.'岁' : '') : '';
    }

    $createTime = $userJobDocList[$v['id']]['create_time'];
    $diffMinutes = (time() - $createTime) / 60;
</php>

<php>if ($v['type'] == 1) {</php>

<div class="official-resume-card">
  <!-- 卡片头部 - 姓名、简历类型、状态信息一行显示 -->
  <div class="resume-header">
    <div class="resume-title-line">
      <h3 class="resume-name">{:$v['name'] ?: '未填写姓名'}</h3>
      <!-- 简历类型标识 -->
      <if condition="$serviceStationRow['zsb_type'] eq 1 and isset($v['resume_type'])">
        <span class="official-type-badge <if condition='$v.resume_type eq 1'>type-own<else/>type-zjb</if>">
          <if condition="$v.resume_type eq 1">
            自有简历
          <else/>
            招就办-{$serviceStationList[$v['service_station_id']]['contract_name']|default=""}
          </if>
        </span>
      </if>
      <!-- 状态信息 -->
      <span class="status-item-inline status-{:$stateMap[$v['job_state']]['style']}">
        <i class="status-icon">ℹ️</i>
        <span>{:$stateMap[$v['job_state']]['text']} {:$v['job_state_text']}</span>
      </span>
    </div>
  </div>

  <!-- 基本信息区域 -->
  <div class="resume-info-grid">
    <div class="info-group">
      <div class="info-row">
        <span class="info-label">毕业院校：</span>
        <span class="info-value">
          {:$v['graduate_school'] ?: '未填写'}
          <php>if($v['major']) {</php>
          <span class="major-info">（{:$v['major']}）</span>
          <php>}</php>
        </span>
      </div>
      <div class="info-row">
        <span class="info-label">基本信息：</span>
        <span class="info-value">
          <php>if($v['gender']) {</php>
          <span class="tag-item">{:$v['gender']}</span>
          <php>}</php>
          <php>if($age && $age != '未知') {</php>
          <span class="tag-item">{$age}</span>
          <php>}</php>
          <php>if($v['marital_status']) {</php>
          <span class="tag-item">{:$v['marital_status']}</span>
          <php>}</php>
          <php>if($v['education_level']) {</php>
          <span class="tag-item">{:$v['education_level']}</span>
          <php>}</php>
        </span>
      </div>
    </div>
    <div class="info-group physical-info">
      <div class="info-row">
        <span class="info-label">身高体重：</span>
        <span class="info-value">
          <if condition="$v['height'] gt 0">
            身高{$v['height']}CM
          <else/>
            <span class="missing">身高未填</span>
          </if>
          <span class="separator">｜</span>
          <if condition="$v['weight'] gt 0">
            体重{$v['weight']}KG
          <else/>
            <span class="missing">体重未填</span>
          </if>
        </span>
      </div>
    </div>
  </div>

  <!-- 备注内容显示 -->
  <php>if(isset($serviceStationRemarks[$v['id']]) && $serviceStationRemarks[$v['id']]) {</php>
  <div class="resume-note-display">
    <div class="note-title">
      <i class="note-icon">📝</i>
      <span>备注信息</span>
      <span class="note-private-tag">仅自己可见</span>
    </div>
    <div class="note-text">{:$serviceStationRemarks[$v['id']]}</div>
  </div>
  <php>}</php>

  <!-- 求职诉求 -->
  <div class="resume-demand">
    <div class="demand-title">
      <i class="demand-icon">🎯</i>
      <span>求职诉求</span>
    </div>
    <div class="demand-text">
      <php>if($v['remark']) {</php>
        {:$v['remark']}
      <php>} else {</php>
        <span class="demand-empty">求职需求未知，请联系客服或下方沟通简历提交信息</span>
      <php>}</php>
    </div>
  </div>



  <!-- 操作按钮区域 -->
  <div class="official-action-area">
    <php>if ($userJobDocList[$v['id']]['is_html'] == 3) {</php>
    <a class="official-btn btn-report" href="http://c.zhongcaiguoke.com/index/jobcontent/id/{:D('Project')->enhash($userJobDocList[$v['id']]['id'])}">
      <i class="btn-icon">📄</i>
      <span>简历报告</span>
    </a>

    <php>if (in_array($v['id'], $trainingOrderList)) {</php>
    <a class="official-btn btn-training-detail" href="{:U('training/detail', ['id' => $trainingOrderMap[$v['id']]])}">
      <i class="btn-icon">🎓</i>
      <span>培训详情</span>
    </a>
    <php>} elseif (isset($cannotEnrollJobIds) && in_array($v['id'], $cannotEnrollJobIds)) {</php>
    <a class="official-btn btn-disabled" href="javascript:void(0);">
      <i class="btn-icon">🚫</i>
      <span>
        <php>
        if($v['job_state'] == 1) echo '该学员培训中';
        elseif($v['job_state'] == 2) echo '该学员已入职';
        else echo '无法报名';
        </php>
      </span>
    </a>
    <php>} else {</php>
    <a class="official-btn btn-training" href="javascript:void(0);" onclick="baoming('{$v['id']}');">
      <i class="btn-icon">➕</i>
      <span>报名培训</span>
    </a>
    <php>}</php>
    <php>}</php>

    <a class="official-btn btn-note" href="javascript:void(0);" onclick="showNoteModal({$v.id}, '{$serviceStationRemarks[$v[id]]|default=\'\'}')"
      <i class="btn-icon">📝</i>
      <span>备注（仅自己可见）</span>
    </a>
    <a class="official-btn btn-chat" href="{:U('index/remark', ['id' => $v['id']])}">
      <i class="btn-icon">💬</i>
      <span>简历沟通{:$v['need_reply'] ? '<span style="color:#071501;font-weight:bold;">（需要您回复）</span>' : ''}</span>
      
    </a>
  </div>

  <!-- 创建方式标识 -->
  <div class="creation-tag">
    <i class="creation-icon">📱</i>
    <span>该简历为学员通过服务站公众号自主创建</span>
  </div>
</div>

<php>}else{</php>
<php>if ($userJobDocList[$v['id']]['status'] == 3) {</php>
<div class="official-resume-card">
  <!-- 卡片头部 - 姓名、简历类型、状态信息一行显示 -->
  <div class="resume-header">
    <div class="resume-title-line">
      <h3 class="resume-name">{:$v['name'] ?: '未填写姓名'}</h3>
      <!-- 简历类型标识 -->
      <if condition="$serviceStationRow['zsb_type'] eq 1 and isset($v['resume_type'])">
        <span class="official-type-badge <if condition='$v.resume_type eq 1'>type-own<else/>type-zjb</if>">
          <if condition="$v.resume_type eq 1">
            自有简历
          <else/>
            招就办-{$serviceStationList[$v['service_station_id']]['contract_name']|default=""}
          </if>
        </span>
      </if>
      <!-- 状态信息 -->
      <span class="status-item-inline status-{:$stateMap[$v['job_state']]['style']}">
        <i class="status-icon">ℹ️</i>
        <span>{:$stateMap[$v['job_state']]['text']}</span>
      </span>
    </div>

 
  </div>

      <!-- 服务说明 -->
<php>if($v['job_state_text']) {</php>
  <div class="resume-server">
    <div class="demand-title">
      <span>服务说明：{:$v['job_state_text']}</span>
    </div>
  </div>
   <php>}</php>

  <!-- 基本信息区域 -->
  <div class="resume-info-grid">
    <div class="info-group">
      <div class="info-row">
        <span class="info-label">毕业院校：</span>
        <span class="info-value">
          {:$v['graduate_school'] ?: '未填写'}
          <php>if($v['major']) {</php>
          <span class="major-info">（{:$v['major']}）</span>
          <php>}</php>
        </span>
      </div>
      <div class="info-row">
        <span class="info-label">基本信息：</span>
        <span class="info-value">
          <php>if($v['gender']) {</php>
          <span class="tag-item">{:$v['gender']}</span>
          <php>}</php>
          <php>if($age && $age != '未知') {</php>
          <span class="tag-item">{$age}</span>
          <php>}</php>
          <php>if($v['marital_status']) {</php>
          <span class="tag-item">{:$v['marital_status']}</span>
          <php>}</php>
          <php>if($v['education_level']) {</php>
          <span class="tag-item">{:$v['education_level']}</span>
          <php>}</php>
        </span>
      </div>
    </div>
    <div class="info-group physical-info">
      <div class="info-row">
        <span class="info-label">身高体重：</span>
        <span class="info-value">
          <if condition="$v['height'] gt 0">
            身高{$v['height']}CM
          <else/>
            <span class="missing">身高未填</span>
          </if>
          <span class="separator">｜</span>
          <if condition="$v['weight'] gt 0">
            体重{$v['weight']}KG
          <else/>
            <span class="missing">体重未填</span>
          </if>
        </span>
      </div>
    </div>
  </div>

  <!-- 备注内容显示 -->
  <php>if(isset($serviceStationRemarks[$v['id']]) && $serviceStationRemarks[$v['id']]) {</php>
  <div class="resume-note-display">
    <div class="note-title">
      <i class="note-icon">📝</i>
      <span>备注信息</span>
      <span class="note-private-tag">仅自己可见</span>
    </div>
    <div class="note-text">{:$serviceStationRemarks[$v['id']]}</div>
  </div>
  <php>}</php>

  <!-- 求职诉求 -->
  <div class="resume-demand">
    <div class="demand-title">
      <i class="demand-icon">🎯</i>
      <span>求职诉求</span>
    </div>
    <div class="demand-text">
      <php>if($v['remark']) {</php>
        {:$v['remark']}
      <php>} else {</php>
        <span class="demand-empty">求职需求未知，请联系客服或下方沟通简历提交信息</span>
      <php>}</php>
    </div>
  </div>



  <!-- 操作按钮区域 -->
  <div class="official-action-area">
    <php>if ($userJobDocList[$v['id']]['is_html'] == 3) {</php>
    <a class="official-btn btn-report" href="http://c.zhongcaiguoke.com/index/jobcontent/id/{:D('Project')->enhash($userJobDocList[$v['id']]['id'])}">
      <i class="btn-icon">📄</i>
      <span>简历报告</span>
    </a>

    <php>if (in_array($v['id'], $trainingOrderList)) {</php>
    <a class="official-btn btn-training-detail" href="{:U('training/detail', ['id' => $trainingOrderMap[$v['id']]])}">
      <i class="btn-icon">🎓</i>
      <span>培训详情</span>
    </a>
    <php>} elseif (isset($cannotEnrollJobIds) && in_array($v['id'], $cannotEnrollJobIds)) {</php>
    <a class="official-btn btn-disabled" href="javascript:void(0);">
      <i class="btn-icon">🚫</i>
      <span>
        <php>
        if($v['job_state'] == 1) echo '该学员培训中';
        elseif($v['job_state'] == 2) echo '该学员已入职';
        else echo '无法报名';
        </php>
      </span>
    </a>
    <php>} else {</php>
    <a class="official-btn btn-training" href="javascript:void(0);" onclick="baoming('{$v['id']}');">
      <i class="btn-icon">➕</i>
      <span>报名培训</span>
    </a>
    <php>}</php>
    <php>}</php>

    <a class="official-btn btn-note" href="javascript:void(0);" onclick="showNoteModal({$v.id}, '{$serviceStationRemarks[$v[id]]|default=\'\'}')"
      <i class="btn-icon">📝</i>
      <span>备注（仅自己可见）</span>
    </a>
    <a class="official-btn btn-chat" href="{:U('index/remark', ['id' => $v['id']])}">
      <i class="btn-icon">💬</i>
      <span>简历沟通{:$v['need_reply'] ? '<span style="color:#071501;font-weight:bold;">（需要您回复）</span>' : ''}</span>

    </a>
  </div>

  <php>if ($userJobDocList[$v['id']]['is_html'] == 3) {</php>
  <div
    class="status-tag status-jobdoc"
    style="
      margin-top: 10px;
      padding-top: 12px;
      padding-bottom: 12px;
      font-size: 14px;
    "
  >
    简历文件名称：（{:date('Y-m-d H:i:s',
    $userJobDocList[$v['id']]['create_time'])}上传）<br />
    <a target="_blank" href="http://we.zhongcaiguoke.com{:$userJobDocList[$v['id']]['content']}" style="color: #07c160; text-decoration: underline;">{:$userJobDocList[$v['id']]['file_names']}</a>
  </div>
  <php>}</php>
</div>

<php>} else {</php>

<div class="resume-item" style="background-color: rgb(219, 215, 215)">
  <div
    class="status-tag status-jobdoc"
    style="
      margin: 10px 0;
      padding-top: 12px;
      padding-bottom: 12px;
      font-size: 14px;
    "
  >
    简历文件名称：（{:date('Y-m-d H:i:s',
    $userJobDocList[$v['id']]['create_time'])}上传）<br />
    <a target="_blank" href="http://we.zhongcaiguoke.com{:$userJobDocList[$v['id']]['content']}" style="color: #07c160; text-decoration: underline;">{:$userJobDocList[$v['id']]['file_names']}</a>
  </div>

  <!-- 检查简历模板匹配状态 -->
  <if condition="isset($userJobDocList[$v['id']]['check_doc_succ']) && $userJobDocList[$v['id']]['check_doc_succ'] eq 2">
    <!-- 简历格式不符合标准模板 -->
    <div
      class="status-tag status-jobdocerr"
      style="
        margin: 10px 0;
        padding-top: 12px;
        padding-bottom: 12px;
        font-size: 14px;
        text-align: center;
        background-color: #ffebee;
        border: 1px solid #f44336;
        border-radius: 4px;
      "
    >
      <div style="color: #d32f2f; font-weight: bold; margin-bottom: 8px;">
        ❌ 简历上传失败！
      </div>
      <div style="color: #666; margin-bottom: 10px;">
        您上传的简历格式不符合标准要求，请使用公司通用简历模板重新上传
      </div>
      <div style="margin-bottom: 10px;">
        <a
          target="_blank"
          href="http://c.zhongcaiguoke.com/data/%E5%BA%94%E8%81%98%E4%BA%BA%E5%91%98%E6%8A%A5%E5%90%8D%E8%A1%A8%EF%BC%88%E9%80%9A%E7%94%A8%EF%BC%89.docx"
          class="status-tag status-communicating"
          style="color: #4caf50; font-weight: bold; text-decoration: underline;"
          >📥 下载公司通用简历模板</a
        >
      </div>
      <div>
        <a
          href="{:U('index/deljobdoc', ['id' => $v['id']])}"
          class="status-tag status-communicating"
          onclick="return confirm('确认要删除该简历吗？删除后可重新上传标准格式简历')"
          style="color: rgb(184, 13, 62); font-weight: bold"
          >🗑️ 删除并重新上传</a
        >
      </div>
    </div>
  <else/>
    <!-- 原有的分析中状态显示 -->
    <div class="basic-info">
      <div class="left-info" style="width: 100%; text-align: center">
        <div>
          简历正在分析中，一般15分钟内分析完成……
          <!-- 警告提示 -->
          <if condition="$diffMinutes gt 15">
            <br /><br />超时则表示简历异常，请
            <a
              target="_blank"
              href="http://c.zhongcaiguoke.com/data/%E5%BA%94%E8%81%98%E4%BA%BA%E5%91%98%E6%8A%A5%E5%90%8D%E8%A1%A8%EF%BC%88%E9%80%9A%E7%94%A8%EF%BC%89.docx"
              class="status-tag status-communicating"
              style="color: green"
              >下载标准模板</a
            >
            填写并上传<br /><br />30分钟如仍未完成分析，则可删除该上传信息。
          </if>
        </div>
      </div>
    </div>

    <!-- 警告提示 -->
    <if condition="$diffMinutes gt 30">
      <div
        class="status-tag status-jobdocerr"
        style="
          margin-top: 10px;
          padding-top: 12px;
          padding-bottom: 12px;
          font-size: 14px;
          text-align: center;
        "
      >
        异常！请删除后按标准模板重新上传
        <a
          href="{:U('index/deljobdoc', ['id' => $v['id']])}"
          class="status-tag status-communicating"
          onclick="return confirm('确认要删除该简历吗？操作不可逆！请谨慎')"
          style="color: rgb(184, 13, 62); font-weight: bold"
          >删除简历</a
        >
      </div>
    </if>
  </if>
</div>

<php>}}}}</php>
